defaults:
  - _self_
  - model/diffuser@model: diffuser
  # - model/cvae@model: cvae
  # - data: sceneleap
  - data: sceneleapplus
  - distributed: default
  - wandb: default
  # - override hydra/hydra_logging: colorlog
  # - override hydra/job_logging: colorlog
# 基础配置
device: cuda:0
#ncols: 50
epochs: 1000
#print_freq: 500
#validate_freq: 1
save_root: &save_root ./experiments/test_test_test88_2
save_top_n: 10
#log_dir: logs
seed: 42
rot_type: &rot_type r6d
mode: &mode camera_centric_scene_mean_normalized
batch_size: &batch_size 48
use_negative_prompts: &use_negative_prompts false
target_num_grasps: &target_num_grasps 1
fix_num_grasps: &fix_num_grasps True
use_object_mask: &use_object_mask False
# 启用固定抓取数量
# fix_num_grasps: true
# target_num_grasps: 50

# # 或者禁用（使用动态数量）
# fix_num_grasps: false
# target_num_grasps: null

# 点云数据格式说明:
# - 输入点云格式: (B, N, 6) = xyz + rgb
# - PointNet2会自动分离xyz坐标和rgb特征
# - 支持文本条件的扩散模型训练

checkpoint_path: ???

# Trainer配置
trainer:
  # 基础训练参数
  max_epochs: ${epochs}
  accelerator: auto  # auto, gpu, cpu
  devices: auto      # auto, 数字, 或列表 [0,1,2,3]
  strategy: auto     # auto, ddp, fsdp, ddp_sharded
  precision: 32      # 16, 32, 64

  # 训练控制
  check_val_every_n_epoch: 1
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1
  benchmark: true
  log_every_n_steps: 20

  # 分布式特定配置
  sync_batchnorm: false
  find_unused_parameters: true





hydra:
  output_subdir: null
  job:
    chdir: False

  hydra_logging:
    version: 1
    disable_existing_loggers: true
  job_logging:
    version: 1
    disable_existing_loggers: true


## 多抓取全局配置
#multi_grasp_config:
#  # 数据配置
#  data:
#    num_grasps_per_scene: 8  # 每个场景的抓取数量
#    grasp_sampling_strategy: "diverse"  # diverse, random, quality_based
#    augmentation:
#      enabled: true
#      rotation_range: 15  # 度
#      translation_range: 0.02  # 米
#      noise_std: 0.001
#
#  # 训练配置
#  training:
#    multi_grasp_enabled: true  # 是否启用多抓取训练
#    backward_compatibility: true  # 是否保持向后兼容
#    progressive_training: false  # 是否使用渐进式训练
#
#  # 推理配置
#  inference:
#    parallel_sampling: true  # 是否使用并行采样
#    output_format: "multi_grasp"  # multi_grasp, best_grasp, top_k
#    post_processing:
#      nms_enabled: true  # 是否启用非极大值抑制
#      nms_threshold: 0.1  # NMS阈值
#      diversity_filtering: true  # 是否进行多样性过滤